#!/usr/bin/env php
<?php

// 测试模式支持
if (getenv('TEST_MODE')) {
    class TestVersionManager {
        // 测试类实现
    }
    class TestEnvironmentChecker {
        public function check() {
            if (getenv('TEST_ENV_CHECK_FAIL')) {
                return ['is_ok' => false];
            }
            if (getenv('TEST_MISSING_EXTS')) {
                return ['is_ok' => true, 'missing_recommended_extensions' => explode(',', getenv('TEST_MISSING_EXTS'))];
            }
            return ['is_ok' => true];
        }
        public function getDetailedInfo() {
            return "模拟环境检查失败详情";
        }
    }
}

// 确保在CLI环境中运行
if (PHP_SAPI !== 'cli') {
    echo 'Warning: PVM should be run in a Command Line Interface.' . PHP_EOL;
    exit(1);
}

// 自动加载
$autoloadPaths = [
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/../../../autoload.php',
];

$loaded = false;
foreach ($autoloadPaths as $file) {
    if (file_exists($file)) {
        require $file;
        $loaded = true;
        break;
    }
}

if (!$loaded) {
    echo 'You need to set up the project dependencies using Composer:' . PHP_EOL . PHP_EOL;
    echo '    composer install' . PHP_EOL . PHP_EOL;
    echo 'You can learn all about Composer on https://getcomposer.org/.' . PHP_EOL;
    exit(1);
}

// 检查基础PHP环境
try {
    $environmentChecker = getenv('TEST_MODE') ?
        new TestEnvironmentChecker() :
        new VersionManager\Core\System\EnvironmentChecker();
    $checkResult = $environmentChecker->check();

    // 获取命令 - 无参数时进入交互模式
    $command = isset($argv[1]) ? $argv[1] : 'interactive';

    // 检查是否有 -y 参数
    $hasYesOption = false;
    foreach ($argv as $arg) {
        if ($arg === '--yes' || $arg === '-y') {
            $hasYesOption = true;
            break;
        }
        // 检查组合短选项中是否包含 y
        if (strpos($arg, '-') === 0 && strpos($arg, 'y') !== false && strlen($arg) > 2) {
            $hasYesOption = true;
            break;
        }
    }

    // 如果命令不是'init'或'help'或'--version'或'-v'，则检查环境
    if ($command !== 'init' && $command !== 'help' && $command !== '--version' && $command !== '-v') {
        // 如果环境不满足要求
        if (!$checkResult['is_ok']) {
            echo "\033[31m错误: PVM运行环境不满足要求\033[0m" . PHP_EOL . PHP_EOL;
            echo $environmentChecker->getDetailedInfo() . PHP_EOL;

            // 检查是否使用自动确认
            if ($hasYesOption) {
                echo "\033[33m自动确认修复环境问题...\033[0m" . PHP_EOL;
                $shouldFix = true;
            } else {
                // 提示用户是否立即修复
                echo "\033[33m是否立即修复环境问题？(y/n) \033[0m";
                $handle = fopen("php://stdin", "r");
                $line = trim(fgets($handle));
                fclose($handle);

                $shouldFix = getenv('TEST_AUTO_FIX') ?
                    (getenv('TEST_AUTO_FIX') === '1') :
                    (strtolower($line) === 'y' || strtolower($line) === 'yes');
            }

            if ($shouldFix) {
                echo "正在尝试修复环境问题...\n";

                if (getenv('TEST_MODE')) {
                    echo "环境问题已修复\n";
                    $checkResult = ['is_ok' => true];
                } else {
                    // 创建InitCommand实例并执行修复
                    $initCommand = new VersionManager\Console\Commands\InitCommand();
                    $fixResult = $initCommand->execute(['--fix']);
                }

                if ($fixResult === 0) {
                    echo "环境问题已修复，继续执行原命令...\n\n";
                    // 重新检查环境
                    $checkResult = $environmentChecker->check();
                    if (!$checkResult['is_ok']) {
                        echo "\033[31m错误: 环境问题未完全修复，请手动运行 'pvm init --fix' 命令\033[0m" . PHP_EOL;
                        exit(1);
                    }
                } else {
                    echo "\033[31m错误: 修复环境问题失败，请手动运行 'pvm init --fix' 命令\033[0m" . PHP_EOL;
                    exit(1);
                }
            } else {
                echo "\033[33m请运行 'pvm init --fix' 命令初始化PVM运行环境\033[0m" . PHP_EOL;
                exit(1);
            }
        }

        // 检查PVM是否已经初始化
        $homeDir = getenv('HOME');
        $pvmDir = $homeDir . '/.pvm';

        if (!is_dir($pvmDir) || !is_dir($pvmDir . '/versions') || !is_dir($pvmDir . '/config')) {
            echo "\033[33m警告: PVM尚未初始化\033[0m" . PHP_EOL;
            echo "\033[33m请运行 'pvm init' 命令初始化PVM运行环境\033[0m" . PHP_EOL . PHP_EOL;
        }

        // 如果有缺失的推荐扩展，显示警告
        if (!empty($checkResult['missing_recommended_extensions'])) {
            echo "\033[33m警告: 缺失推荐的PHP扩展: " .
                 implode(', ', $checkResult['missing_recommended_extensions']) . "\033[0m" . PHP_EOL;
            echo "\033[33m这些扩展不是必需的，但安装它们可能会提高PVM的功能\033[0m" . PHP_EOL . PHP_EOL;
        }
    }

    // 解析日志级别参数
    $logLevel = \VersionManager\Core\Logger\LogLevel::NORMAL;
    foreach ($argv as $arg) {
        if (strpos($arg, '--verbose') === 0 || strpos($arg, '-v') === 0) {
            $logLevel = \VersionManager\Core\Logger\LogLevel::VERBOSE;
        } elseif (strpos($arg, '--silent') === 0 || strpos($arg, '-s') === 0) {
            $logLevel = \VersionManager\Core\Logger\LogLevel::SILENT;
        } elseif (strpos($arg, '--debug') === 0) {
            $logLevel = \VersionManager\Core\Logger\LogLevel::DEBUG;
        }
    }

    // 设置日志级别
    \VersionManager\Core\Logger\Logger::setLevel($logLevel);

    // 初始化文件日志系统
    $command = isset($argv[1]) ? $argv[1] : 'interactive';
    $commandArgs = array_slice($argv, 2);
    \VersionManager\Core\Logger\FileLogger::init($command, $commandArgs);

    // 引导应用程序
    $app = new VersionManager\Console\Application();
    $exitCode = $app->run($argv);

    // 记录命令结束
    \VersionManager\Core\Logger\FileLogger::logCommandEnd($exitCode);

    exit($exitCode);
} catch (Exception $e) {
    echo "\033[31m错误: " . $e->getMessage() . "\033[0m" . PHP_EOL;

    // 记录异常到日志
    \VersionManager\Core\Logger\FileLogger::error("未捕获的异常: " . $e->getMessage());
    \VersionManager\Core\Logger\FileLogger::error("异常堆栈: " . $e->getTraceAsString());
    \VersionManager\Core\Logger\FileLogger::logCommandEnd(1);

    exit(1);
}
