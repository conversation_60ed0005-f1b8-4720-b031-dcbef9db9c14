# PVM-Mirror 环境变量配置文件示例
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 基础配置
# =============================================================================

# 运行环境 (development/production/testing)
PVM_MIRROR_ENV=development

# 调试模式 (true/false)
PVM_MIRROR_DEBUG=false

# =============================================================================
# 目录配置
# =============================================================================

# 数据目录 (留空使用默认: /app/data)
PVM_MIRROR_DATA_DIR=

# 日志目录 (留空使用默认: /app/logs)
PVM_MIRROR_LOG_DIR=

# 缓存目录 (留空使用默认: /app/cache)
PVM_MIRROR_CACHE_DIR=

# =============================================================================
# 日志配置
# =============================================================================

# 日志级别 (debug/info/warning/error)
PVM_MIRROR_LOG_LEVEL=info

# =============================================================================
# 服务器配置
# =============================================================================

# 监听主机
PVM_MIRROR_HOST=0.0.0.0

# 监听端口
PVM_MIRROR_PORT=34403

# 公开URL (用于生成下载链接)
PVM_MIRROR_PUBLIC_URL=http://localhost:34403

# 最大并发连接数
PVM_MIRROR_MAX_CONNECTIONS=100

# 请求超时时间（秒）
PVM_MIRROR_TIMEOUT=30

# =============================================================================
# HTTPS配置
# =============================================================================

# 是否启用HTTPS (true/false)
PVM_MIRROR_ENABLE_HTTPS=false

# SSL证书路径
PVM_MIRROR_SSL_CERT=

# SSL密钥路径
PVM_MIRROR_SSL_KEY=

# =============================================================================
# 缓存配置
# =============================================================================

# 缓存最大大小 (字节)
PVM_MIRROR_CACHE_SIZE=104857600

# 缓存TTL (秒)
PVM_MIRROR_CACHE_TTL=3600

# =============================================================================
# 同步配置
# =============================================================================

# 同步间隔 (小时)
PVM_MIRROR_SYNC_INTERVAL=24

# 最大重试次数
PVM_MIRROR_MAX_RETRIES=3

# 重试间隔 (秒)
PVM_MIRROR_RETRY_INTERVAL=300

# =============================================================================
# 开发环境特定配置
# =============================================================================

# 开发模式下的额外配置
# 这些配置仅在 PVM_MIRROR_ENV=development 时生效

# 启用详细日志
# PVM_MIRROR_LOG_LEVEL=debug

# 减少缓存时间以便测试
# PVM_MIRROR_CACHE_TTL=60

# 增加同步频率以便测试
# PVM_MIRROR_SYNC_INTERVAL=1
