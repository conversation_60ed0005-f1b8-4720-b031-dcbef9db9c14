services:
  pvm-mirror:
    build:
      context: ../../
      dockerfile: docker/pvm-mirror/Dockerfile
    container_name: pvm-mirror-prod
    ports:
      - "${PVM_MIRROR_PORT:-34403}:${PVM_MIRROR_PORT:-34403}"
    env_file:
      - .env
    volumes:
      # 持久化数据目录
      - pvm_mirror_data:/app/data
      # 持久化日志目录
      - pvm_mirror_logs:/app/logs
      # 持久化缓存目录
      - pvm_mirror_cache:/app/cache
      # 映射配置目录（只读）
      - ../../configMirror:/app/configMirror:ro
    environment:
      # 生产环境配置
      - PVM_MIRROR_ENV=production
      - PVM_MIRROR_DEBUG=false
      
      # 日志配置
      - PVM_MIRROR_LOG_LEVEL=info
      
      # 服务器配置
      - PVM_MIRROR_HOST=0.0.0.0
      - PVM_MIRROR_PORT=34403
      - PVM_MIRROR_MAX_CONNECTIONS=200
      - PVM_MIRROR_TIMEOUT=60
      
      # 缓存配置（生产环境使用较大的缓存）
      - PVM_MIRROR_CACHE_SIZE=**********  # 1GB
      - PVM_MIRROR_CACHE_TTL=3600         # 1小时
      
      # 同步配置（生产环境较少的同步频率）
      - PVM_MIRROR_SYNC_INTERVAL=24       # 24小时
      - PVM_MIRROR_MAX_RETRIES=5
      - PVM_MIRROR_RETRY_INTERVAL=300     # 5分钟
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PVM_MIRROR_PORT:-34403}/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - pvm-mirror-network
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

networks:
  pvm-mirror-network:
    driver: bridge

volumes:
  # 生产环境持久化卷
  pvm_mirror_data:
    driver: local
  pvm_mirror_logs:
    driver: local
  pvm_mirror_cache:
    driver: local
